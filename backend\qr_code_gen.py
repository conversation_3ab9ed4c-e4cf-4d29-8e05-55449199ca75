import qrcode
import os
import uuid
from datetime import datetime
from typing import Optional

def generate_qr_code_for_html_report(
    html_content: str,
    job_position: str,
    base_url: str = "http://localhost:8000",
    output_dir: str = "static/reports"
) -> tuple[str, str]:
    """
    Generate a QR code that points to an HTML report.

    Args:
        html_content: The HTML content of the report
        job_position: The job position being analyzed
        base_url: Base URL where the report will be served
        output_dir: Directory to save the HTML report and QR code

    Returns:
        tuple: (qr_code_path, html_report_url)
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(f"{output_dir}/qr_codes", exist_ok=True)

    # Generate unique filename based on job position and timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    safe_job_name = "".join(c for c in job_position if c.isalnum() or c in (' ', '-', '_')).rstrip()
    safe_job_name = safe_job_name.replace(' ', '_')

    # Generate unique ID to avoid conflicts
    unique_id = str(uuid.uuid4())[:8]

    html_filename = f"{safe_job_name}_{timestamp}_{unique_id}.html"
    qr_filename = f"{safe_job_name}_{timestamp}_{unique_id}_qr.png"

    # Save HTML report
    html_path = os.path.join(output_dir, html_filename)
    with open(html_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

    # Create URL for the HTML report
    html_report_url = f"{base_url}/reports/{html_filename}"

    # Generate QR code
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(html_report_url)
    qr.make(fit=True)

    # Create QR code image
    qr_img = qr.make_image(fill_color="black", back_color="white")
    qr_path = os.path.join(output_dir, "qr_codes", qr_filename)
    qr_img.save(qr_path)

    return qr_path, html_report_url

def generate_simple_qr_code(url: str, filename: str = "qr_code.png") -> str:
    """
    Generate a simple QR code for any URL.

    Args:
        url: The URL to encode in the QR code
        filename: Output filename for the QR code image

    Returns:
        str: Path to the generated QR code image
    """
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(url)
    qr.make(fit=True)

    qr_img = qr.make_image(fill_color="black", back_color="white")
    qr_img.save(filename)

    return filename

# Example usage (keeping the original functionality)
if __name__ == "__main__":
    # Original example
    qr = qrcode.make('https://example.com/aaa.pdf')
    qr.save('pdf_qr.png')
    print("Basic QR code generated: pdf_qr.png")